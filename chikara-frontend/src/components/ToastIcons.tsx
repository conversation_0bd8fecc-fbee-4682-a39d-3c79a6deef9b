import React, { useId } from "react";

interface IconProps {
    size?: number;
    className?: string;
}

// Success Icon - Checkmark with circle and sparkles
export const SuccessIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => {
    const gradientId = useId();
    const filterId = useId();

    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <defs>
                <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#10b981" />
                    <stop offset="100%" stopColor="#059669" />
                </linearGradient>
                <filter id={filterId}>
                    <feGaussianBlur stdDeviation="2" result="coloredBlur" />
                    <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                    </feMerge>
                </filter>
            </defs>

            {/* Background circle with gradient */}
            <circle cx="12" cy="12" r="10" fill={`url(#${gradientId})`} filter={`url(#${filterId})`} />

            {/* Checkmark */}
            <path
                d="M8.5 12.5L10.5 14.5L15.5 9.5"
                stroke="white"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="none"
            />

            {/* Sparkles */}
            <g fill="white" opacity="0.8">
                <circle cx="6" cy="6" r="1">
                    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="2s" repeatCount="indefinite" />
                </circle>
                <circle cx="18" cy="8" r="0.8">
                    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="1.5s" repeatCount="indefinite" />
                </circle>
                <circle cx="19" cy="16" r="1.2">
                    <animate attributeName="opacity" values="0.8;0.4;0.8" dur="2.5s" repeatCount="indefinite" />
                </circle>
            </g>
        </svg>
    );
};

// Error Icon - Warning triangle with exclamation
export const ErrorIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => {
    const gradientId = useId();
    const filterId = useId();

    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <defs>
                <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#ef4444" />
                    <stop offset="100%" stopColor="#dc2626" />
                </linearGradient>
                <filter id={filterId}>
                    <feGaussianBlur stdDeviation="1" result="coloredBlur" />
                    <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                    </feMerge>
                </filter>
            </defs>

            {/* Triangle background */}
            <path d="M12 2L22 20H2L12 2Z" fill={`url(#${gradientId})`} filter={`url(#${filterId})`} />

            {/* Exclamation mark */}
            <g fill="white">
                <rect x="11" y="8" width="2" height="6" rx="1" />
                <circle cx="12" cy="17" r="1" />
            </g>
        </svg>
    );
};

// Loading Icon - Simple spinning circle with dots
export const LoadingIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => {
    const gradientId = useId();

    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <defs>
                <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#1d4ed8" />
                </linearGradient>
            </defs>

            {/* Spinning circle with dots */}
            <g>
                <circle cx="12" cy="6" r="2" fill={`url(#${gradientId})`} />
                <circle cx="12" cy="18" r="2" fill={`url(#${gradientId})`} opacity="0.3" />
                <circle cx="6" cy="12" r="2" fill={`url(#${gradientId})`} opacity="0.6" />
                <circle cx="18" cy="12" r="2" fill={`url(#${gradientId})`} opacity="0.8" />

                <animateTransform
                    attributeName="transform"
                    type="rotate"
                    values="0 12 12;360 12 12"
                    dur="1s"
                    repeatCount="indefinite"
                />
            </g>

            {/* Center dot */}
            <circle cx="12" cy="12" r="1.5" fill={`url(#${gradientId})`} opacity="0.9" />
        </svg>
    );
};

// Info Icon - Information symbol with decorative elements
export const InfoIcon: React.FC<IconProps> = ({ size = 20, className = "" }) => {
    const gradientId = useId();
    const filterId = useId();

    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <defs>
                <linearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8b5cf6" />
                    <stop offset="100%" stopColor="#7c3aed" />
                </linearGradient>
                <filter id={filterId}>
                    <feGaussianBlur stdDeviation="1.5" result="coloredBlur" />
                    <feMerge>
                        <feMergeNode in="coloredBlur" />
                        <feMergeNode in="SourceGraphic" />
                    </feMerge>
                </filter>
            </defs>

            {/* Background circle */}
            <circle cx="12" cy="12" r="10" fill={`url(#${gradientId})`} filter={`url(#${filterId})`} />

            {/* Information symbol */}
            <g fill="white">
                <circle cx="12" cy="8" r="1.5" />
                <rect x="11" y="11" width="2" height="7" rx="1" />
            </g>

            {/* Decorative rings */}
            <circle cx="12" cy="12" r="12" fill="none" stroke="#8b5cf6" strokeWidth="0.5" opacity="0.4">
                <animate attributeName="r" values="12;14;12" dur="3s" repeatCount="indefinite" />
                <animate attributeName="opacity" values="0.4;0.1;0.4" dur="3s" repeatCount="indefinite" />
            </circle>
        </svg>
    );
};
