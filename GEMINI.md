# Product Overview

Chikara Battle Academy is a mobile + web MMORPG/PBBG set in an anime-style 'battle academy' in modern day Tokyo. The game combines traditional MMORPG elements with exploration mechanics and social features.

## Core Systems

- **Character Progression**: Leveling, stats (Strength, Dexterity, Intelligence, Stamina), talents & skills
- **Combat**: Turn-based PvP/PvE battles with status effects and strategic depth
- **Explore Map Mode**: Interactive district-based exploration with dynamic nodes, travel system, and time-limited opportunities
- **Crafting**: Recipe-based time-gated crafting system with material gathering
- **Economy**: Multi-currency system (Yen, Gang Creds, Classroom Points) with auction house
- **Social**: Gangs, friends/rivals, global chat, private messaging
- **Exploration**: District-based exploration with dynamic nodes (battle, scavenging, mining, foraging)
- **Questing**: Data-driven quest system with main, daily, and tutorial quests

## Key Features

- Property ownership, shrine system, casino, pet system
- Classroom activities and exams for Classroom Points
- Banking system, bounties, infirmary, jail mechanics
- Time-limited events and missions
- Leaderboards and arcade minigames

The game emphasizes player interaction through gangs, economy, and competitive elements while providing both casual and hardcore gameplay paths.

# Technology Stack

## Build System

- **Monorepo**: Turborepo with Bun package manager
- **Package Manager**: Bun 1.2.19+ (preferred over npm/yarn)
- **Workspaces**: 4 main applications (frontend, backend, admin-panel, landing)

## Frontend Stack

- **Framework**: React 19+ with TypeScript
- **Build Tool**: Vite 7+
- **Styling**: Tailwind CSS 4+ with Radix UI components
- **State Management**: Zustand, React Tracked
- **Data Fetching**: TanStack Query with ORPC client
- **Routing**: React Router DOM 7+
- **Real-time**: Socket.io client
- **Testing**: Vitest, Playwright for E2E

## Backend Stack

- **Runtime**: Bun (Node.js 21.2.0+ compatible)
- **Framework**: Express 5+ with TypeScript
- **Database**: MySQL with Prisma ORM 6.11+
- **Caching**: Redis 5+
- **Queue System**: BullMQ
- **Real-time**: Socket.io server
- **API**: ORPC for type-safe RPC
- **Authentication**: Better Auth
- **Testing**: Vitest with coverage

## Admin Panel

- **Framework**: React 19+ with TypeScript
- **UI**: Radix UI, shadcn/ui components
- **Data Tables**: TanStack Table
- **Charts**: Recharts
- **Build**: Vite

## Common Commands

### Development

```bash
# Start all services
bun dev

# Start specific service
bun dev:frontend
bun dev:backend
bun dev:admin

# Database operations
bun run seed         # Seed database
```

### Build & Deploy

```bash
# Build all
bun build

# Build specific
bun build:frontend
bun build:backend
bun build:admin
```

### Testing

```bash
# Run all tests
bun test

# Watch mode
bun test:watch

# Coverage (backend)
bun run coverage
```

### Code Quality

```bash
bun lint           # ESLint
bun format         # Prettier
bun type-check     # TypeScript
```

## Key Dependencies

- **oRPC**: Type-safe RPC between frontend/backend
- **Prisma**: Database ORM with MySQL
- **Socket.io**: Real-time communication
- **BullMQ**: Job queue system
- **Better Auth**: Authentication system
- **Tailwind CSS**: Utility-first styling
- **Radix UI**: Accessible component primitives

# Project Structure

## Root Level

```
├── chikara-frontend/     # Main game client (React/Vite)
├── chikara-backend/      # Game server (Express/Bun)
├── admin-panel/          # Admin dashboard (React/Vite)
├── chikara-landing/      # Marketing site (Next.js)
├── assets/               # Game assets (images, icons, characters)
├── docs/                 # Documentation and guides
└── turbo.json           # Turborepo configuration
```

## Frontend Structure (`chikara-frontend/`)

```
src/
├── app/                 # App-level configuration and providers
├── components/          # Reusable UI components
├── features/            # Feature-specific components and logic
├── hooks/               # Custom React hooks
├── lib/                 # Utilities and configurations
├── pages/               # Route components
├── types/               # TypeScript type definitions
└── utils/               # Helper functions
```

## Backend Structure (`chikara-backend/`)

```
src/
├── config/              # App configuration (database, redis, etc.)
├── core/                # Core game systems and business logic
├── features/            # Feature-specific modules
├── lib/                 # Shared utilities and services
├── middleware/          # Express middleware
├── queues/              # BullMQ job definitions
├── repositories/        # Data access layer
├── types/               # TypeScript type definitions
├── utils/               # Helper functions
├── routes.ts            # Main API routes
├── admin.routes.ts      # Admin API routes
└── server.ts            # Application entry point
```

## Admin Panel Structure (`admin-panel/`)

```
src/
├── components/          # UI components (tables, forms, charts)
├── hooks/               # Admin-specific hooks
├── lib/                 # Utilities and API client
├── pages/               # Admin dashboard pages
├── types/               # TypeScript definitions
└── helpers/             # Helper functions
```

## Assets Structure (`assets/`)

```
├── AI/                  # AI-generated backgrounds and locations
├── characters/          # Character sprites organized by name
└── icons/               # Game item icons organized by category
```

## Key Conventions

### File Naming

- **Components**: PascalCase (e.g., `GameBoard.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useGameState.ts`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Types**: PascalCase (e.g., `UserProfile.ts`)
- **API Routes**: kebab-case (e.g., `user-profile.ts`)

### Folder Organization

- **Features**: Group related components, hooks, and utilities together
- **Shared**: Common components and utilities in `lib/` or `components/`
- **Types**: Centralized in `types/` folders with clear naming
- **Tests**: Co-located with source files using `.test.ts` or `.spec.ts`

### Import Patterns

- Use absolute imports from `src/` root
- Group imports: external libraries, internal modules, relative imports
- Prefer named exports over default exports for utilities

### Database Schema

- **Prisma**: Single schema file at `chikara-backend/prisma/schema.prisma`
- **Seeds**: Environment-specific seeders in `prisma/seeders/`

### Configuration Files

- **Environment**: `.env` files per workspace with `.env.example` templates
- **TypeScript**: Workspace-specific `tsconfig.json` files
- **ESLint/Prettier**: Consistent configuration across workspaces
