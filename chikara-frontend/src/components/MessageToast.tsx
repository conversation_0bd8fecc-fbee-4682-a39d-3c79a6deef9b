import { DisplayAvatar } from "@/components/DisplayAvatar";
import { motion } from "framer-motion";
import type { NavigateFunction } from "react-router-dom";

interface MessageToastProps {
    toastId: string;
    userData?: { username: string; avatar: string | null; [key: string]: unknown };
    onReply: (id: string, navigate: NavigateFunction) => void;
    navigate: NavigateFunction;
}

export const MessageToast = ({ toastId, userData, onReply, navigate }: MessageToastProps) => {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, x: 100 }}
            className="
                relative overflow-hidden rounded-2xl border-2 backdrop-blur-md
                bg-gradient-to-br from-blue-950 via-blue-900 to-indigo-950
                border-blue-400/60 shadow-blue-500/40
                shadow-2xl cursor-pointer group
                hover:scale-[1.03] hover:shadow-3xl transition-all duration-300 ease-out
                max-w-md ring-1 ring-white/10
            "
        >
            {/* Enhanced overlay for depth and color */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/15 via-transparent to-blue-600/10 pointer-events-none" />
            <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/30 pointer-events-none" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-white/5 pointer-events-none" />

            {/* Content */}
            <div className="relative flex items-center gap-3 p-3">
                <DisplayAvatar className="size-10 rounded-full ring-2 ring-blue-400/30 flex-shrink-0" src={userData} />

                <div className="flex-1 min-w-0">
                    <p className="text-white font-display text-sm font-medium leading-snug tracking-normal drop-shadow-md">
                        {userData?.username || "Unknown User"}
                    </p>
                    <p className="text-white/70 font-display text-xs leading-snug tracking-normal drop-shadow-md mt-1">
                        Just sent you a message!
                    </p>
                </div>

                <button
                    type="button"
                    aria-label={`View message from ${userData?.username || "unknown user"}`}
                    className="
                        flex-shrink-0 px-3 py-1.5
                        bg-gradient-to-r from-blue-400 to-indigo-400
                        text-white text-xs font-medium rounded-lg
                        border border-blue-400/60
                        hover:from-blue-300 hover:to-indigo-300
                        hover:scale-105 transition-all duration-200
                        shadow-lg drop-shadow-sm
                    "
                    onClick={() => onReply(toastId, navigate)}
                >
                    View
                </button>
            </div>
        </motion.div>
    );
};
