import apImg from "@/assets/icons/UI/APicon3.png";
import hpImg from "@/assets/icons/UI/HPicon.png";
import { cn } from "@/lib/utils";

type StatBarType = "health" | "actionPoints";

interface StatBarTypeConfig {
    fill: string;
    image: string;
    alt: string;
}

interface SidebarStatBarCollapsedProps {
    type: StatBarType;
    percentage: number;
    current: number;
    max: number;
}

export default function SidebarStatBarCollapsed({ type, percentage, current, max }: SidebarStatBarCollapsedProps) {
    const statBarTypes: Record<StatBarType, StatBarTypeConfig> = {
        health: {
            fill: "bg-green-500",
            image: hpImg,
            alt: "Health",
        },
        actionPoints: {
            fill: "bg-blue-500",
            image: apImg,
            alt: "Action Points",
        },
    };

    const config = statBarTypes[type];
    const fillPercentage = Math.min(Math.max(percentage * 100, 0), 100);

    return (
        <div
            role="progressbar"
            aria-label={config.alt}
            aria-valuenow={current}
            aria-valuemin={0}
            aria-valuemax={max}
            className="relative w-full h-3 bg-gray-700 rounded-sm overflow-hidden group border border-gray-600 dark:border-gray-700"
            title={`${config.alt}: ${current}/${max}`}
        >
            {/* Background bar */}
            <div className="absolute inset-0 bg-gray-600 dark:bg-gray-800" />

            {/* Fill bar */}
            <div
                className={cn("absolute inset-y-0 left-0 transition-all duration-300", config.fill)}
                style={{ width: `${fillPercentage}%` }}
            />

            {/* Icon */}
            <div className="absolute inset-0 flex items-center justify-start pl-0.5">
                <img src={config.image} alt={config.alt} className="h-2.5 w-auto opacity-90" />
            </div>

            {/* Text overlay - always show percentage */}
            <div className="absolute inset-0 flex items-center justify-center pl-2">
                <span className="text-xs font-medium text-white text-stroke-sm">{Math.round(fillPercentage)}%</span>
            </div>
        </div>
    );
}
