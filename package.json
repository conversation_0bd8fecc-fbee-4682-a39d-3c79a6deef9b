{"name": "chikara-academy", "private": true, "type": "module", "packageManager": "bun@1.2.19", "workspaces": ["chikara-frontend", "chikara-backend", "admin-panel", "chikara-landing"], "scripts": {"build": "turbo build", "dev": "turbo dev", "test": "turbo test", "test:watch": "turbo test:watch", "lint": "turbo lint", "format": "turbo format", "type-check": "turbo type-check", "dev:frontend": "turbo dev --filter=chikara-frontend", "dev:backend": "turbo dev --filter=chikara-backend", "dev:admin": "turbo dev --filter=admin-panel", "build:frontend": "turbo build --filter=chikara-frontend", "build:backend": "turbo build --filter=chikara-backend", "build:admin": "turbo build --filter=admin-panel", "test:frontend": "turbo test --filter=chikara-frontend", "test:backend": "turbo test --filter=chikara-backend", "test:admin": "turbo test --filter=admin-panel", "generate": "turbo run generate --filter=chikara-backend"}, "devDependencies": {"@typescript/native-preview": "^7.0.0-dev.20250712.1", "prettier": "^3.6.2", "turbo": "^2.5.4"}, "trustedDependencies": ["@biomejs/biome", "@firebase/util", "@tailwindcss/oxide", "core-js", "protobufjs", "unrs-resolver"]}