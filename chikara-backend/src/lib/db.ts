import extendedPrismaClient from "./prisma/prisma.js";
/* eslint-disable unicorn/prefer-export-from */
/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Prisma } from "@prisma/client";

// ================================================ Extended Prisma Client ================================================

export type ExtendedPrismaClient = typeof extendedPrismaClient;

export const db = extendedPrismaClient;

// ================================================ Extended Models ================================================

export type ExtUserModel = NonNullable<Awaited<ReturnType<ExtendedPrismaClient["user"]["findUnique" | "findFirst"]>>>;
export type ExtUserItemModel = NonNullable<
    Awaited<ReturnType<ExtendedPrismaClient["user_item"]["findUnique" | "findFirst"]>>
>;
export type ExtItemModel = NonNullable<Awaited<ReturnType<ExtendedPrismaClient["item"]["findUnique" | "findFirst"]>>>;
export type ExtQuestModel = NonNullable<Awaited<ReturnType<ExtendedPrismaClient["quest"]["findUnique" | "findFirst"]>>>;
export type ExtShopListingModel = NonNullable<
    Awaited<ReturnType<ExtendedPrismaClient["shop_listing"]["findUnique" | "findFirst"]>>
>;

// ================================================ Model Interfaces ================================================

export type TransactionClient = Omit<
    ExtendedPrismaClient,
    "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends"
>;

export type UserModel<T extends Prisma.userFindManyArgs = {}> = Prisma.userGetPayload<T>;

export type UserItemModel<T extends Prisma.user_itemFindManyArgs = {}> = Prisma.user_itemGetPayload<T>;

export type ItemModel<T extends Prisma.itemFindManyArgs = {}> = Prisma.itemGetPayload<T>;

export type QuestModel<T extends Prisma.questFindManyArgs = {}> = Prisma.questGetPayload<T>;

export type ShopListingModel<T extends Prisma.shop_listingFindManyArgs = {}> = Prisma.shop_listingGetPayload<T>;

export type ActionLogModel<T extends Prisma.action_logFindManyArgs = {}> = Prisma.action_logGetPayload<T>;

export type AuctionItemModel<T extends Prisma.auction_itemFindManyArgs = {}> = Prisma.auction_itemGetPayload<T>;

export type BankTransactionModel<T extends Prisma.bank_transactionFindManyArgs = {}> =
    Prisma.bank_transactionGetPayload<T>;

export type BattleLogModel<T extends Prisma.battle_logFindManyArgs = {}> = Prisma.battle_logGetPayload<T>;

export type BountyModel<T extends Prisma.bountyFindManyArgs = {}> = Prisma.bountyGetPayload<T>;

export type ChatMessageModel<T extends Prisma.chat_messageFindManyArgs = {}> = Prisma.chat_messageGetPayload<T>;

export type ChatRoomModel<T extends Prisma.chat_roomFindManyArgs = {}> = Prisma.chat_roomGetPayload<T>;

export type CraftingRecipeModel<T extends Prisma.crafting_recipeFindManyArgs = {}> =
    Prisma.crafting_recipeGetPayload<T>;

export type CreatureModel<T extends Prisma.creatureFindManyArgs = {}> = Prisma.creatureGetPayload<T>;

export type DailyMissionModel<T extends Prisma.daily_missionFindManyArgs = {}> = Prisma.daily_missionGetPayload<T>;

export type DailyQuestModel<T extends Prisma.daily_questFindManyArgs = {}> = Prisma.daily_questGetPayload<T>;

export type DropChanceModel<T extends Prisma.drop_chanceFindManyArgs = {}> = Prisma.drop_chanceGetPayload<T>;

export type EquippedItemModel<T extends Prisma.equipped_itemFindManyArgs = {}> = Prisma.equipped_itemGetPayload<T>;

export type GameStatsModel<T extends Prisma.game_statsFindManyArgs = {}> = Prisma.game_statsGetPayload<T>;

export type GangModel<T extends Prisma.gangFindManyArgs = {}> = Prisma.gangGetPayload<T>;

export type GangInviteModel<T extends Prisma.gang_inviteFindManyArgs = {}> = Prisma.gang_inviteGetPayload<T>;

export type GangLogModel<T extends Prisma.gang_logFindManyArgs = {}> = Prisma.gang_logGetPayload<T>;

export type GangMemberModel<T extends Prisma.gang_memberFindManyArgs = {}> = Prisma.gang_memberGetPayload<T>;

export type JobModel<T extends Prisma.jobFindManyArgs = {}> = Prisma.jobGetPayload<T>;

export type LotteryModel<T extends Prisma.lotteryFindManyArgs = {}> = Prisma.lotteryGetPayload<T>;

export type LotteryEntryModel<T extends Prisma.lottery_entryFindManyArgs = {}> = Prisma.lottery_entryGetPayload<T>;

export type NotificationModel<T extends Prisma.notificationFindManyArgs = {}> = Prisma.notificationGetPayload<T>;

export type PollModel<T extends Prisma.pollFindManyArgs = {}> = Prisma.pollGetPayload<T>;

export type PollResponseModel<T extends Prisma.poll_responseFindManyArgs = {}> = Prisma.poll_responseGetPayload<T>;

export type PrivateMessageModel<T extends Prisma.private_messageFindManyArgs = {}> =
    Prisma.private_messageGetPayload<T>;

export type ProfileCommentModel<T extends Prisma.profile_commentFindManyArgs = {}> =
    Prisma.profile_commentGetPayload<T>;

export type PushTokenModel<T extends Prisma.push_tokenFindManyArgs = {}> = Prisma.push_tokenGetPayload<T>;

export type QuestProgressModel<T extends Prisma.quest_progressFindManyArgs = {}> = Prisma.quest_progressGetPayload<T>;

export type QuestObjectiveModel<T extends Prisma.quest_objectiveFindManyArgs = {}> =
    Prisma.quest_objectiveGetPayload<T>;

export type QuestObjectiveProgressModel<T extends Prisma.quest_objective_progressFindManyArgs = {}> =
    Prisma.quest_objective_progressGetPayload<T>;

export type RecipeItemModel<T extends Prisma.recipe_itemFindManyArgs = {}> = Prisma.recipe_itemGetPayload<T>;

export type RegistrationCodeModel<T extends Prisma.registration_codeFindManyArgs = {}> =
    Prisma.registration_codeGetPayload<T>;

export type ShopModel<T extends Prisma.shopFindManyArgs = {}> = Prisma.shopGetPayload<T>;

export type ShrineDonationModel<T extends Prisma.shrine_donationFindManyArgs = {}> =
    Prisma.shrine_donationGetPayload<T>;

export type ShrineGoalModel<T extends Prisma.shrine_goalFindManyArgs = {}> = Prisma.shrine_goalGetPayload<T>;

export type SuggestionModel<T extends Prisma.suggestionFindManyArgs = {}> = Prisma.suggestionGetPayload<T>;

export type SuggestionCommentModel<T extends Prisma.suggestion_commentFindManyArgs = {}> =
    Prisma.suggestion_commentGetPayload<T>;

export type SuggestionVoteModel<T extends Prisma.suggestion_voteFindManyArgs = {}> =
    Prisma.suggestion_voteGetPayload<T>;

export type TalentModel<T extends Prisma.talentFindManyArgs = {}> = Prisma.talentGetPayload<T>;

export type TraderRepModel<T extends Prisma.trader_repFindManyArgs = {}> = Prisma.trader_repGetPayload<T>;

export type SessionModel<T extends Prisma.sessionFindManyArgs = {}> = Prisma.sessionGetPayload<T>;

export type AccountModel<T extends Prisma.accountFindManyArgs = {}> = Prisma.accountGetPayload<T>;

export type VerificationModel<T extends Prisma.verificationFindManyArgs = {}> = Prisma.verificationGetPayload<T>;

export type UserRecipeModel<T extends Prisma.user_recipeFindManyArgs = {}> = Prisma.user_recipeGetPayload<T>;

export type UserCompletedCourseModel<T extends Prisma.user_completed_courseFindManyArgs = {}> =
    Prisma.user_completed_courseGetPayload<T>;

export type UserTalentModel<T extends Prisma.user_talentFindManyArgs = {}> = Prisma.user_talentGetPayload<T>;

export type UserAchievementsModel<T extends Prisma.user_achievementsFindManyArgs = {}> =
    Prisma.user_achievementsGetPayload<T>;

export type UserCraftingQueueModel<T extends Prisma.user_crafting_queueFindManyArgs = {}> =
    Prisma.user_crafting_queueGetPayload<T>;

export type PropertyModel<T extends Prisma.propertyFindManyArgs = {}> = Prisma.propertyGetPayload<T>;

export type UserPropertyModel<T extends Prisma.user_propertyFindManyArgs = {}> = Prisma.user_propertyGetPayload<T>;

export type PetModel<T extends Prisma.petFindManyArgs = {}> = Prisma.petGetPayload<T>;

export type UserPetModel<T extends Prisma.user_petFindManyArgs = {}> = Prisma.user_petGetPayload<T>;

export type FriendRequestModel<T extends Prisma.friend_requestFindManyArgs = {}> = Prisma.friend_requestGetPayload<T>;

export type FriendModel<T extends Prisma.friendFindManyArgs = {}> = Prisma.friendGetPayload<T>;

export type RivalModel<T extends Prisma.rivalFindManyArgs = {}> = Prisma.rivalGetPayload<T>;

export type StatusEffectModel<T extends Prisma.status_effectFindManyArgs = {}> = Prisma.status_effectGetPayload<T>;

export type UserStatusEffectModel<T extends Prisma.user_status_effectFindManyArgs = {}> =
    Prisma.user_status_effectGetPayload<T>;

export type QuestRewardModel<T extends Prisma.quest_rewardFindManyArgs = {}> = Prisma.quest_rewardGetPayload<T>;

export type UserSkillModel<T extends Prisma.user_skillFindManyArgs = {}> = Prisma.user_skillGetPayload<T>;

export type StaticExploreNodeModel<T extends Prisma.explore_static_nodeFindManyArgs = {}> =
    Prisma.explore_static_nodeGetPayload<T>;

export type PlayerExploreNodeModel<T extends Prisma.explore_player_nodeFindManyArgs = {}> =
    Prisma.explore_player_nodeGetPayload<T>;

export type UserEquippedAbilitiesModel<T extends Prisma.user_equipped_abilitiesFindManyArgs = {}> =
    Prisma.user_equipped_abilitiesGetPayload<T>;
