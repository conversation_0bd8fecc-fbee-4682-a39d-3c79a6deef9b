import { gameConfig } from "../../../src/config/gameConfig";

// Extract all config values and categorize them
const extractConfigValues = () => {
    const configEntries: Array<{
        key: string;
        value: unknown;
        category: string;
        isPublic: boolean;
    }> = [];

    // Iterate through each config section
    Object.entries(gameConfig).forEach(([sectionName, sectionConfig]) => {
        // Add public values
        Object.entries(sectionConfig.public).forEach(([key, value]) => {
            configEntries.push({
                key,
                value,
                category: sectionName.replace('Config', ''),
                isPublic: true,
            });
        });

        // Add hidden values
        Object.entries(sectionConfig.hidden).forEach(([key, value]) => {
            configEntries.push({
                key,
                value,
                category: sectionName.replace('Config', ''),
                isPublic: false,
            });
        });
    });

    return configEntries;
};

const defaultGameConfig = extractConfigValues();

export default defaultGameConfig;